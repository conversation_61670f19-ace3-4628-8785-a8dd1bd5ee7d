{"info": {"map_dir": "sogoshikenro-emergency-lane-mrm-southbound", "obstacle_map": [], "chassis_info": {"turn_signal": "TURN_NONE", "advice": "KEEP_ENGAGED", "speed_mps": 22.222200393676758, "timestamp_sec": 1739323286.326092, "driving_mode": "COMPLETE_AUTO_DRIVE"}, "localization_info": {"measurement_time": 1739323286.3260992, "timestamp_sec": 1739323286.3260992, "x": 438681.9640943818, "y": 4037727.456423929, "z": 0.0, "qw": 0.36081069035016017, "qx": 0.0, "qy": 0.0, "qz": -0.9326390758107023, "heading": -0.8325223451416375, "vx": 14.9558141442669, "vy": -16.436234776215432, "vz": 0.0, "ax": 0.0, "ay": 0.0, "az": 0.0, "angular_vx": 0.0, "angular_vy": 0.0, "angular_vz": 0.0}, "num_runs": 300, "planning_config": {"nullopt": false, "data": {"replan_by_long": false, "use_projection": true, "print_planning_input_info": false, "initial_position_definition": "AD_START_POINT", "lane_changed_allowed_when_ready_to_engage": false, "reference_line_lateral_threshold": 10.0, "route_lane_manager_config": {"look_backward_distance": 75.0, "look_forward_short_distance": 180.0, "look_forward_long_distance": 250.0, "allow_off_route_lane_change": false}, "lane_change_config": {"cancelation_threshold": 0.5, "disable_lc_cancel": true, "total_planning_time": 7.0, "turn_signal_time": 3.0, "lane_keep_lateral_threshold": 0.03, "judge_duration_for_lane_change_end_threshold": 3.0, "heading_adjustment_of_cancel_path": 0.0005, "maximum_derivation_of_heading_adjustment_of_cancel_path": 0.0001, "enable_export_lon_lat_data": true, "use_s_critical_for_lane_change": false, "use_d_safe_rear_for_lane_change": true, "use_d_safe_front_for_lane_change": true, "use_d_safe_ego_for_lane_change": true, "lane_change_distance_parameters": {"lane_change_default": {"right": {"upper": -500.0, "lower": -1500.0}, "left": {"upper": 1000.0, "lower": 0.0}}, "lane_change_jari": {"right": {"upper": -500.0, "lower": -1000.0}, "left": {"upper": 500.0, "lower": 0.0}}, "lane_change_takatsuki": {"right": {"upper": -400.0, "lower": -800.0}, "left": {"upper": 1000.0, "lower": 0.0}}, "lane_change_inasa": {"right": {"upper": -500.0, "lower": -1200.0}, "left": {"upper": 410.0, "lower": 0.0}}}, "lane_changeable_parameters": {"max_distance": 10000.0, "min_d_rear": 70, "min_d_front": 70, "obstacle_deceleration_strength": 2.5, "time_to_start_deceleration": 1.0, "target_time_headway": 1.0, "d_safe_rear_parameters": {"d_min": 60, "reaction_time": 1.0, "acc_min_rear": -1.0}, "d_safe_front_parameters": {"d_min": 30, "reaction_time": 1.0, "acc_min_ego": -2.0}, "d_safe_ego_parameters": {"d_target_ratio": 0.2}}}, "use_map_speed": false, "use_long_winker": true, "default_map_speed_limit": 22.22, "speed_limit_driver_trigger_config": {"use_speed_limit_driver_trigger": true, "boundaries": [20.8333, 18.0556, 15.2778], "limits": [13.8889, 16.6667, 19.4444, 22.2222]}, "trigger_config": {"enable_driver_trigger": true, "enable_geo_fencing": false, "enable_automatic_lane_change": false, "abort_lane_change_attempt_after_sec": 30.0}}}, "lane_change_driver_triggers": [{"key": 5.0, "value": {"min_duration_sec": 10.0, "direction": "left"}}, {"key": 10.0, "value": {"min_duration_sec": 10.0, "direction": "right"}}], "expected_results": {"transition_planning_module_status": [0, 1, 2, 0]}}}