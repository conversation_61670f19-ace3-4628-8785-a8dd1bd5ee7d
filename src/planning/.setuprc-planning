SCRIPT_PATH=$(cd `dirname ${BASH_SOURCE:-$0}`; pwd)

function t-planning-new-closed-loop() {
    cd $SCRIPT_PATH
    python3 -m pip show colorama >/dev/null 2>&1 || python3 -m pip install --user colorama
    python3 -m pip show pyfzf >/dev/null 2>&1 || python3 -m pip install --user pyfzf
    python3 tools/run_planning_closed_loop.py --verbose --arch $@
}

function t-planning-old-closed-loop() {
    cd $SCRIPT_PATH
    python3 -m pip show colorama >/dev/null 2>&1 || python3 -m pip install --user colorama
    python3 -m pip show pyfzf >/dev/null 2>&1 || python3 -m pip install --user pyfzf
    python3 tools/run_planning_closed_loop.py --verbose $@
}

function t-plan-new() {
    t-planning-new-closed-loop $@
}


function t-plan-old() {
    t-planning-old-closed-loop $@
}
