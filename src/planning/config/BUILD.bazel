load("@rules_cc//cc:defs.bzl", "cc_library")

package(default_visibility = ["//visibility:public"])

GPERFTOOLS_COPTS = ["-DENABLE_GPERFTOOLS"]

PLANNING_LINKOPTS = ["-lprofiler"]

PLANNING_COPTS = [
    "-DMODULE_NAME=\\\"planning\\\"",
] + GPERFTOOLS_COPTS

cc_library(
    name = "planning_config",  # PlanningConfig
    srcs = [
    ],
    hdrs = [
        "planning_config.hpp",
    ],
    copts = PLANNING_COPTS,
    deps = [
        "//src/planning:planning_module_exception",  # PlanningModuleException
        "@cereal",
    ],
)

cc_library(
    name = "planning_on_local_map_config",  # PlanningOnLocalMapConfig
    srcs = [
    ],
    hdrs = [
        "planning_on_local_map_config.hpp",
    ],
    copts = PLANNING_COPTS,
    deps = [
        "@cereal",
    ],
)

cc_library(
    name = "planner_config",  # PlannerConfig
    srcs = ["planner_config.cpp"],
    hdrs = ["planner_config.hpp"],
    copts = PLANNING_COPTS,
    deps = [
        "//src/planning:planning_module_exception",  # PlanningModuleException
        "@cereal",
    ],
)

cc_library(
    name = "smoother_config",
    srcs = ["smoother_config.cpp"],
    hdrs = ["smoother_config.hpp"],
    deps = [
        "//src/planning:planning_module_exception",  # PlanningModuleException
        "//src/planning/config:planner_config",  # PlannerConfig
        "@cereal",
    ],
)

cc_library(
    name = "miscellaneous_config",
    srcs = ["miscellaneous_config.cpp"],
    hdrs = ["miscellaneous_config.hpp"],
    deps = [
        "//src/planning:planning_dir_prefix",  # FULL_PLANNING_DIR_PREFIX
        "//src/planning:planning_module_exception",  # PlanningModuleException
        "@cereal",
    ],
)
