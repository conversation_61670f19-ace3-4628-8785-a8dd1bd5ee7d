// Copyright (c) 2025 T2 Inc. All rights reserved.
#pragma once

#include <vector>

// cereal
#include <cereal/archives/json.hpp>
#include <cereal/types/memory.hpp>

#include "src/planning/config/planner_config.hpp"  // IpoptConfig

namespace t2::planning {

struct FemPosDeviationSmootherConfig {
  double weight_fem_pos_deviation = 1.0e10;
  double weight_ref_deviation = 1.0;
  double weight_path_length = 1.0;

  // osqp settings
  int max_iter = 500;
  // time_limit set to be 0.0 meaning no time limit
  double time_limit = 0.0;
  bool verbose = false;
  bool scaled_termination = true;
  bool warm_start = true;

  template <typename T>
  void serialize(T& archive) {
    archive(CEREAL_NVP(weight_fem_pos_deviation), CEREAL_NVP(weight_ref_deviation),
            CEREAL_NVP(weight_path_length), CEREAL_NVP(max_iter), CEREAL_NVP(time_limit),
            CEREAL_NVP(verbose), CEREAL_NVP(scaled_termination), CEREAL_NVP(warm_start));
  }
};  // end-struct FemPosDeviationSmootherConfig

struct ReferenceLineSmootherConfig {
  // The output resolution for discrete point smoother reference line is
  // directly decided by max_constraint_interval
  double max_constraint_interval = 0.25;
  double longitudinal_boundary_bound = 0.2;
  double max_lateral_boundary_bound = 0.1;
  double min_lateral_boundary_bound = 0.01;
  // The output resolution for qp smoother reference line.
  uint32_t num_of_total_points = 500;
  double curb_shift = 0.2;
  double lateral_buffer = 0.2;
  // The output resolution for spiral smoother reference line.
  double resolution = 0.02;
  FemPosDeviationSmootherConfig fem_pos_deviation_smoothing;

  bool use_ipopt = false;
  IpoptOptions ipopt_options;

  template <typename T>
  void serialize(T& archive) {
    archive(CEREAL_NVP(max_constraint_interval), CEREAL_NVP(longitudinal_boundary_bound),
            CEREAL_NVP(max_lateral_boundary_bound), CEREAL_NVP(min_lateral_boundary_bound),
            CEREAL_NVP(num_of_total_points), CEREAL_NVP(curb_shift), CEREAL_NVP(lateral_buffer),
            CEREAL_NVP(resolution), CEREAL_NVP(fem_pos_deviation_smoothing), CEREAL_NVP(use_ipopt),
            CEREAL_NVP(ipopt_options));
  }
};

}  // namespace t2::planning
