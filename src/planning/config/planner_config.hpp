// Copyright (c) 2025 T2 Inc. All rights reserved.
#pragma once

#include <cereal/archives/json.hpp>
#include <cereal/types/memory.hpp>
#include <cereal/types/vector.hpp>

namespace t2::planning {

struct IpoptOptions {
  double tol = 1e-7;                         // tolerance, e.g., 1e-5 to 1e-8
  uint32_t max_iter = 50;                    // maximum number of iterations, e.g., 50 to 100
  double constr_viol_tol = 1e-4;             // constraint violation tolerance, 1e-4 to 1e-6
  std::string linear_solver = "pardisomkl";  // linear solver
  bool approximate_hessian = false;          // approximate Hessian by quasi-Newton
  uint32_t print_level = 0;  // Ipopt's print level: 0 (no info) to 12 (most detailed)

  template <typename T>
  void serialize(T& archive) {
    archive(CEREAL_NVP(tol), CEREAL_NVP(max_iter), CEREAL_NVP(constr_viol_tol),
            CEREAL_NVP(linear_solver), CEREAL_NVP(approximate_hessian), CEREAL_NVP(print_level));
  }
};

struct PlannerConfig {
  // Path length of fallback trajectory for visualization.
  // Note that the actual stop distance is not equal to this parameter.

  double dt = 0.1;         ///< timestep in speed planners [s]
  uint32_t n_points = 61;  // number of steps

  struct PathPlannerConfig {
    // Longitudial intervals [m] of PathBounds
    double longitudinal_step_size = 0.5;
    // Mininum Longitudinal interval [m]
    double minimum_step_size = 0.1;
    // Number of points sampled along the longitudinal axis
    uint32_t num_steps = 300;

    struct PathPlannerNLPOptions {
      IpoptOptions ipopt_options;
      double lane_changing_threshold = 0.001;  ///< lateral distance to decide whether the ego is
                                               ///< still lane changing [m]
      double D2l_max = 1e-4;                   ///< maximun of velocity of heading
      double D3l_max = 3e-4;                   ///< maximun of acceleration of heading
      double D4l_max = 9e-4;                   ///< maximun of jerk of heading
      std::vector<double> scaling_factors{1, 1, 100};  ///< scaling factors for D2l, D3l, and D4l

      template <typename T>
      void serialize(T& archive) {
        archive(CEREAL_NVP(ipopt_options), CEREAL_NVP(lane_changing_threshold), CEREAL_NVP(D2l_max),
                CEREAL_NVP(D3l_max), CEREAL_NVP(D4l_max), CEREAL_NVP(scaling_factors));
      }
    };

    PathPlannerNLPOptions path_planner_nlp_options;

    template <typename T>
    void serialize(T& archive) {
      archive(CEREAL_NVP(longitudinal_step_size), CEREAL_NVP(minimum_step_size),
              CEREAL_NVP(num_steps),
              // PathPlannerNLP
              CEREAL_NVP(path_planner_nlp_options));
    }
  };  // struct PathPlannerConfig

  // tasks in order

  // MyBoundaryMapperConfig
  struct MyBoundaryMapperConfig {
    double lat_buffer = 0.4;
    double lon_buffer = 1.0;
    double rough_step_length = 2.5;
    double fine_step_length = 0.25;
    // ST values from isuzu-giga/planning.conf)
    // Longitudinal horizon for speed decision making (meter)
    double speed_lon_decision_horizon = 400.0;
    // Trajectory time length
    double planning_max_time = 10.0;

    template <typename T>
    void serialize(T& archive) {
      archive(CEREAL_NVP(lat_buffer), CEREAL_NVP(lon_buffer), CEREAL_NVP(rough_step_length),
              CEREAL_NVP(fine_step_length), CEREAL_NVP(speed_lon_decision_horizon),
              CEREAL_NVP(planning_max_time));
    }
  };

  struct TrajectoryPlannerConfig {
    struct Bounds {
      double a_min = -2.6;  // lower bound of acceleration (max brake) [m/s^2]
      double a_max = 2.6;   // upper bound of acceleration [m/s^2]
      double j_min = -2.6;  // lower bound of jerk [m/s^3]
      double j_max = 2.6;   // upper bound of jerk [m/s^3]

      template <typename T>
      void serialize(T& archive) {
        archive(CEREAL_NVP(a_min), CEREAL_NVP(a_max), CEREAL_NVP(j_min), CEREAL_NVP(j_max));
      }
    };

    // ACC requirement parameters
    struct ACCRequirementParameters {
      double a_ego_comf = -2.6;   ///< ego's comfortable braking [m/s^2]
      double a_ego_max = -7.2;    ///< ego's max braking [m/s^2]
      double a_front_max = -8.3;  ///< front vehicle's max braking, m/s^2
      double t_react = 0.8;       ///< reaction time, 0.8 sec
      double d0_target = 7;       ///< d1 in D_target [m]
      double d0_min = 4;          ///< d2 in D_min [m]

      // ttc
      double max_ttc = 30.0;    // the maximum ttc above which we consider the ttc maximum. [s]
      double min_ttc = 5.0;     // the minimum ttc bellow which we consider the ttc minimum. [s]
      double a_min_ttc = -0.5;  // the minimum allowed decceleration when ttc is
                                // considered maximum. [m/s^2]
      double a_min_ttc_d_min = -1.6;  // the minimum allowed dec [m/s^2]
      bool use_is_merging = true;     // Wether or not use the predicted trajectory in the speed
                                      // planner when the obstacle is considered merging.

      bool use_ttc_safety_distance = true;
      double A = 0.1;  // amplitude of tanh, it directly determines the relative speed
                       // command between the ego vehicle and the front vehicle.
      double B = 1.0;  // the slope of tanh, it control the approach when clsoe
                       // to d_target.

      double v_target_margin = 0.0;
      double obstacle_distance_threshold = 150.0;  // Obstacle detection threshold [m]

      template <typename T>
      void serialize(T& archive) {
        archive(CEREAL_NVP(a_ego_comf), CEREAL_NVP(a_ego_max), CEREAL_NVP(max_ttc),
                CEREAL_NVP(min_ttc), CEREAL_NVP(a_min_ttc), CEREAL_NVP(a_min_ttc_d_min),
                CEREAL_NVP(use_is_merging), CEREAL_NVP(a_front_max), CEREAL_NVP(t_react),
                CEREAL_NVP(d0_target), CEREAL_NVP(d0_min), CEREAL_NVP(use_ttc_safety_distance),
                CEREAL_NVP(A), CEREAL_NVP(B), CEREAL_NVP(v_target_margin),
                CEREAL_NVP(obstacle_distance_threshold));
      }
    };

    IpoptOptions ipopt_options;
    Bounds bounds;
    ACCRequirementParameters acc_requirement_parameters;
    bool enable_export_ipopt_results = false;
    bool use_sampling_period_fix = false;

    template <typename T>
    void serialize(T& archive) {
      archive(CEREAL_NVP(ipopt_options), CEREAL_NVP(bounds), CEREAL_NVP(acc_requirement_parameters),
              CEREAL_NVP(enable_export_ipopt_results));
    }
  };  // end-struct TrajectoryPlannerConfig

  struct PlanningSpeedConfig {
    // Maximum speed (m/s) in planning.
    double planning_upper_speed_limit = 22.2222;
    double look_ahead_distance = 2000;  // [meters]
    bool target_speed_enabled = true;
    // the idexes of the trajectory used in the objective function of the
    // velocity follower nlp.
    std::vector<int> objective_func_velocity_idx = {1};

    template <class Archive>
    void serialize(Archive& archive) {
      archive(CEREAL_NVP(planning_upper_speed_limit), CEREAL_NVP(objective_func_velocity_idx),
              CEREAL_NVP(look_ahead_distance), CEREAL_NVP(target_speed_enabled));
    }
  };  //   PlanningSpeedConfig

  /* Path planner tasks */
  PathPlannerConfig path_planner_config;
  /* Speed planner tasks */
  TrajectoryPlannerConfig trajectory_planner_config;

  // planning speed config
  PlanningSpeedConfig planning_speed_config;

  // Lateral margin used by the planner
  double lateral_margin = 0.2;

  template <typename T>
  void serialize(T& archive) {
    archive(CEREAL_NVP(dt), CEREAL_NVP(n_points), CEREAL_NVP(path_planner_config),
            CEREAL_NVP(trajectory_planner_config), CEREAL_NVP(planning_speed_config),
            CEREAL_NVP(lateral_margin));
  }

  double get_suitable_longitudinal_step_size(double v) const;
};  // struct PlannerConfig

}  // namespace t2::planning
