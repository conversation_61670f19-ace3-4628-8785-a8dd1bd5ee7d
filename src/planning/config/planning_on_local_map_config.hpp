// Copyright (c) 2025 T2 Inc. All rights reserved.
#pragma once

#include <fstream>

// cereal
#include <cereal/archives/json.hpp>
#include <cereal/types/memory.hpp>
#include <cereal/types/vector.hpp>

namespace t2::planning {

struct PlanningOnLocalMapConfiguration {
  bool use_cartesian_planner = false;

  template <typename T>
  void serialize(T& archive) {
    archive(CEREAL_NVP(use_cartesian_planner));
  }

  PlanningOnLocalMapConfiguration() = default;
  ~PlanningOnLocalMapConfiguration() = default;
};

}  // namespace t2::planning
