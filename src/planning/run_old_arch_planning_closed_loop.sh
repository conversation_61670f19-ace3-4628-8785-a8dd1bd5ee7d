#!/bin/bash

# DOCKER_ID=$(docker ps | grep minimal_docker | awk '{print $1}')

# Usage:
#   docker exec -u $USER desktop bash -c "cd /home/<USER>/Yatagarasu/src/planning; ./run_old_arch_planning_closed_loop.sh"

# bazel run //src/planning/integration_tests:test_planning_closed_loop_apex

IFS=$'\n'

# Function to monitor the log file for a specific string
monitor_log_for_string() {
  local log_file="$1"
  local search_string="$2"

  echo "Monitoring $log_file for the occurrence of '$search_string'..."

  while true; do
    if [[ -f "$log_file" ]]; then
      # Check if the search string exists in the log file
      if grep -q "$search_string" "$log_file"; then
        echo "Detected '$search_string' in $log_file. Killing Apex processes."
        break
      fi
    else
      echo "Log file $log_file does not exist. Retrying..."
    fi
    sleep 5
  done
}

SCRIPT_PATH=$(cd "$(dirname "${BASH_SOURCE[0]:-$0}")" && pwd)
PLANNING_PATH=$SCRIPT_PATH
YATAGARASU_GIT_ROOT=$(realpath "$SCRIPT_PATH/../..")
DATA_DIR="/home/<USER>/data"

echo "SCRIPT_PATH=$SCRIPT_PATH"
echo "YATAGARASU_GIT_ROOT=$YATAGARASU_GIT_ROOT"
cd $SCRIPT_PATH || true

rm -rf $DATA_DIR
mkdir $DATA_DIR

rm -rf /tmp/apex_ida_resource_creator_uds.apex

closed_loop_file_list="planning_closed_loop_file_list.txt"
default_scenario_file="closed_loop_scenarios/ACC/follow_ego_80_other_60.json"

if [ "$#" -gt 1 ]; then
    scenario_file="$1"
    echo "Use input scenario_file=$1"
elif [ -f "$closed_loop_file_list" ]; then
    scenario_file=$(cat "$closed_loop_file_list")

    # Trim leading/trailing whitespace (optional, safe practice)
    scenario_file=$(echo "$scenario_file" | xargs)

    if [ -z "$scenario_file" ]; then
        scenario_file="$default_scenario_file"
        echo "File is empty, use default: $scenario_file"
    else
        echo "Use file content: $scenario_file"
    fi
else
    scenario_file="$default_scenario_file"
    echo "File not found, use default: $scenario_file"
fi

if [ ! -e "$SCRIPT_PATH/$scenario_file" ]; then
  echo "Error: $SCRIPT_PATH/$scenario_file does not exist"
  exit 1
fi

sed -i "s|\"planning_test_input_filename\": \".*\"|\"planning_test_input_filename\": \"src/planning/$scenario_file\"|" integration_tests/planning_test_input_filename.json


## RUN STUFF
# Call SILS
echo ""
echo "BEGIN testing scenario_file=$scenario_file";

wall_start_time=$(date +%s%N)  # Get start time in nanoseconds

log_file="test_planning_closed_loop_apex.log"
nohup bazel run //src/planning/integration_tests:test_planning_closed_loop_apex > $log_file 2>&1 &
# bazel_pid=$!

search_string="Error: Process 'planning_closed_loop_apex'"
monitor_log_for_string "$log_file" "$search_string"

wall_end_time=$(date +%s%N)    # Get end time in nanoseconds
elapsed_time=$(( (wall_end_time - wall_start_time) / 1000000000 ))  # Convert to seconds
echo "Elapsed time: $elapsed_time seconds"
echo "  END testing $scenario_file";
echo ""

# Kill Apex processes
# shellcheck disable=SC2009
for i in $(ps aux | grep planning_closed_loop_apex); do pid=$(echo $i | awk '{print $2}'); echo "kill $pid"; kill -SIGINT $pid; done

rosbag_file="$DATA_DIR/rosbag2/rosbag2_0.mcap"
output_json="${rosbag_file%.mcap}.json"
if [ ! -e "$rosbag_file" ]; then
  echo "Error: $rosbag_file is not generated"
  exit 1
fi

## Run in Bazel's Python environment
planning_topic="/t2/planning"
bazel run //src/planning/tools:process_ros2_mcap -- $rosbag_file --planning_topic $planning_topic

if [ ! -e "$output_json" ]; then
  echo "Error: $output_json is not generated"
  exit 1
fi

## Run in Python virtual environment
cd $PLANNING_PATH || true

# Create a list of required pip packages
required_packages=(
  "click"
  "numpy"
  "plotly"
  "matplotlib"
  "scipy"
)

if [ ! -d "env" ]; then
  python3 -m venv env
fi
# shellcheck disable=SC1090
source env/bin/activate

# Loop through and install missing packages
for package in "${required_packages[@]}"; do
  if ! python3 -c "import $package" &> /dev/null; then
    echo "Installing missing package: $package"
    pip install "$package"
  else
    echo "Package already installed: $package"
  fi
done
output_html="${rosbag_file%.mcap}.html"
python3 tools/plot_mcap.py $output_json --planning_topic $planning_topic

if [ -e "$output_html" ]; then
  echo "Successfully generated $output_html"
else
  echo "Error: $output_html is not generated"
  exit 1
fi
