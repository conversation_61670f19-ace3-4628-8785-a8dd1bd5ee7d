// Copyright (c) 2025 T2 Inc. All rights reserved.

#pragma once

#include <list>
#include <memory>
#include <queue>
#include <string>
#include <unordered_set>
#include <vector>

#include <planning_msgs/msg/planner_evaluation_info.hpp>  // PlannerEvaluationInfo

#include "src/common/proto/geometry.pb.h"  // common::PointENU
#include "src/planning/common/planning_vehicle_state/planning_vehicle_state.hpp"
#include "src/planning/config/planning_config.hpp"
#include "src/planning/planning_macros.hpp"  // REGISTER_*_PLANNING_MSG
#include "src/planning/reference_line/discrete_points_reference_line_smoother.hpp"
#include "src/planning/reference_line/reference_line.hpp"      // ReferenceLine
#include "src/planning/route_lane_manager/route_segments.hpp"  // RouteSegments

namespace t2::planning {

REGISTER_INTER_PLANNING_MSG(PlannerEvaluationInfo);

struct CreateReferenceLineResult {
  // For keeping the past data structure.
  bool is_valid = false;
  std::list<ReferenceLine> list_reference_lines;
  std::list<route_lane_manager::RouteSegments> list_route_segments;
  // For new data structure.
  std::optional<ReferenceLineAndRouteSegments> opt_ego_lane;
  std::optional<ReferenceLineAndRouteSegments> opt_left_lane;
  std::optional<ReferenceLineAndRouteSegments> opt_right_lane;
};

/**
 * @class ReferenceLineProvider
 * @brief The class of ReferenceLineProvider.
 *        It provides smoothed reference line to planning.
 */
class ReferenceLineProvider {
 public:
  ReferenceLineProvider() = default;
  ~ReferenceLineProvider() = default;

  void Init(const bool with_hdmap = true);  ///< with HDMap

  CreateReferenceLineResult CreateReferenceLine(const PlanningVehicleState& vehicle_state,
                                                const PlanningConfiguration& config);

  map::hdmap::LaneInfoConstPtr FindEgoLane(const PlanningVehicleState& vehicle_state);

  PlannerEvaluationInfo GetPlannerEvaluationInfo(
      const PlanningVehicleState& aligned_vehicle_state,
      const map::hdmap::LaneInfoConstPtr& ego_lane_info_ptr);

  /**
   * @brief Create a reference line and the corresponding route segments based
   * on the given lane id, s, and is_on_segment. This calculation traces the
   * sucessors of the given lane id regardless of the routing.
   * @param lane_id The lane id to create the reference line from.
   * @param s The distance along the lane to create the reference line from.
   * @param is_on_segment A boolean that indicates whether the lane is on a
   * segment.
   * @return An optional that contains the reference line and route segments if
   * the process succeeds, or an empty optional otherwise.
   */
  std::optional<ReferenceLineAndRouteSegments>
  CreateFollowingReferenceLineAndRouteSegmentsFromLaneId(const map::hdmap::Id& lane_id, double s,
                                                         bool is_on_segment,
                                                         const double look_backward_distance,
                                                         const double look_forward_distance);

  /**
   * @brief queue the created reference lines and list of route segments in
   * history
   * @param reference_lines the list of reference lines just created by
   * CreateReferenceLinesAndRouteSegments
   * @param list_route_segments the list of groups of route segments just
   * created by CreateReferenceLinesAndRouteSegments
   */
  void QueueReferenceLinesAndRouteSegments(
      const std::list<ReferenceLine>& reference_lines,
      const std::list<route_lane_manager::RouteSegments>& list_route_segments);

  /**
   * @brief Validates the smoothness of a reference line.
   *
   * This function compares a raw reference line with a processed reference line
   * to determine if the smoothness criteria are met. It checks for any abrupt
   * changes or irregularities in the reference line.
   *
   * @param raw_reference_line The raw reference line to be validated.
   * @param reference_line The processed reference line to be compared with the
   * raw reference line.
   * @return True if the reference line is smooth, false otherwise.
   */
  bool ValidateReferenceLineSmoothness(const ReferenceLine& raw_reference_line,
                                       const ReferenceLine& reference_line) const;

  /**
   * @brief Smooths the given raw reference line to generate a smooth reference
   * line.
   *
   * This function takes a raw reference line and applies a smoothing algorithm
   * to generate a smooth reference line.
   *
   * @param raw_reference_line The raw reference line to be smoothed.
   * @param reference_line[out] The resulting smooth reference line.
   * @return True if the smoothing operation is successful, false otherwise.
   */
  bool SmoothReferenceLine(const ReferenceLine& raw_reference_line, ReferenceLine& reference_line);

  /**
   * @brief Retrieves the anchor points from the given reference line.
   *
   * This function returns a vector of anchor points extracted from the provided
   * reference line.
   *
   * @param reference_line The reference line from which to extract the anchor
   * points.
   * @return A vector of anchor points extracted from the reference line.
   */
  std::vector<AnchorPoint> GetAnchorPoints(const ReferenceLine& reference_line) const;

  /**
   * @brief Creates a reference line by smoothing the given route segments.
   *
   * This function takes a set of route segments and generates a smoothed
   * reference line based on those segments. The reference line is used for
   * planning and control purposes.
   *
   * @param segments The route segments to be used for reference line creation.
   * @param[out] reference_line The generated reference line.
   * @return True if the reference line is successfully created, false
   * otherwise.
   */
  bool CreateReferenceLineFromRouteSegmentSmoothing(
      const route_lane_manager::RouteSegments& segments, ReferenceLine& reference_line);

  /**
   * @brief Represents an anchor point on a reference line.
   *
   * An anchor point is a specific point on a reference line that is used for
   * various purposes in planning algorithms. It contains information such as
   * the position, heading, and curvature at that point.
   *
   * @param s distance along the reference line
   * @return an anchor point on the reference line
   */
  AnchorPoint GetAnchorPoint(const ReferenceLine& reference_line, double s) const;

  DiscretePointsReferenceLineSmoother smoother_;
  ReferenceLineSmootherConfig smoother_config_;

  std::queue<std::list<ReferenceLine>> reference_lines_history_;
  std::queue<std::list<route_lane_manager::RouteSegments>> list_route_segments_history_;

  const map::hdmap::HDMap* hdmap_ = nullptr;
  map::hdmap::LaneInfoConstPtr ego_lane_ = nullptr;

  static constexpr size_t kSearchStep = 10;  ///< step size of indices for searching within
                                             ///< reference line's map path points
  static constexpr double kMaxDistanceToRecentPoints = 5.0;  ///< maximum distance to recent points
  static constexpr double kMinAllowedDistanceToPastPoints =
      3.0;  ///< minimum allowed distance to past points
  static constexpr double kReferenceLineDiffCheckStep =
      10.0;  ///< step size to check reference line's smoothness
  static constexpr int kMaxHistoryNum =
      3;  ///< the maximum number of reference lines cached in history

  std::pair<std::vector<map::hdmap::LaneInfoConstPtr>, bool> GetSurroundingLaneInfoConstPtr(
      const common::PointENU& point, const double heading) const;

  bool IsWithinMap(const common::PointENU& point, const double heading) const;

  std::optional<route_lane_manager::RouteSegments> GetRouteSegmentsOfFollowingLane(
      double backward_length, double forward_length,
      const map::hdmap::LaneInfoConstPtr& lane_info_ptr, double lane_s, bool is_on_segment) const;
};

}  // namespace t2::planning
