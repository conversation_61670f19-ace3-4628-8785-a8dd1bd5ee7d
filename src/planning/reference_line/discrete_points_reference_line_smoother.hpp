// Copyright (c) 2025 T2 Inc. All rights reserved.

#pragma once

#include <utility>
#include <vector>

#include <cereal/archives/json.hpp>
#include <cereal/types/memory.hpp>
#include <planning_trajectory_msgs/msg/path_point.hpp>  // PathPoint

#include "src/planning/config/smoother_config.hpp"  // ReferenceLineSmootherConfig
#include "src/planning/math/discretized_points_smoothing/fem_pos_deviation_osqp_solver.hpp"  // FemPosDeviationOsqpSolver
#include "src/planning/math/discretized_points_smoothing/reference_line_nlp.hpp"  // ReferenceLineNLP
#include "src/planning/reference_line/reference_line.hpp"                         // ReferenceLine

namespace t2::planning {

using PathPoint = ::planning_trajectory_msgs::msg::PathPoint;

struct AnchorPoint {
  PathPoint path_point;
  double lateral_bound = 0.0;
  double longitudinal_bound = 0.0;
  // enforce smoother to strictly follow this reference point
  bool enforced = false;
};

class DiscretePointsReferenceLineSmoother {
 public:
  DiscretePointsReferenceLineSmoother();
  ~DiscretePointsReferenceLineSmoother() = default;

  bool Smooth(const ReferenceLine& raw_reference_line, ReferenceLine& smoothed_reference_line);

  void SetAnchorPoints(const std::vector<AnchorPoint>&);

  ReferenceLineSmootherConfig config_;

 private:
  std::vector<std::pair<double, double>> FemPosSmooth(
      const std::vector<std::pair<double, double>>& raw_point2d, const std::vector<double>& bounds);

  void NormalizePoints(std::vector<std::pair<double, double>>* xy_points);

  void DeNormalizePoints(std::vector<std::pair<double, double>>* xy_points);

  bool GenerateRefPointProfile(const ReferenceLine& raw_reference_line,
                               const std::vector<std::pair<double, double>>& xy_points,
                               std::vector<route_lane_manager::MapPathPoint>* reference_points);

  std::vector<AnchorPoint> anchor_points_;

  double zero_x_ = 0.0;
  double zero_y_ = 0.0;

  FemPosDeviationOsqpSolver fem_pos_deviation_osqp_solver_;  ///< cached solver for reusing
  std::mutex mutex_;  ///< allow the solver to solve with one thread at a time

  // Ipopt solver
  Ipopt::SmartPtr<Ipopt::IpoptApplication> app_;  ///< Ipopt application
  std::map<int, Ipopt::SmartPtr<ReferenceLineNLP>>
      map_nlps_;  ///< map number of points to a NLP problem
};

}  // namespace t2::planning
