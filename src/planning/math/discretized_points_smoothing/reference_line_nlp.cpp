#include "reference_line_nlp.hpp"

#include "src/planning/planning_module_exception.hpp"  // PlanningModuleException

namespace t2::planning {

using namespace Ipopt;

template <typename T>
std::string vec_pair_to_string(const std::vector<std::pair<T, T>>& vp,
                               const std::string_view delim = ",") {
  std::stringstream ss;
  ss << std::setprecision(8) << "[";
  for (const auto& [vfirst, vsecond] : vp) {
    ss << "(" << vfirst << "," << vsecond << ")" << delim;
  }
  ss << "]";
  return ss.str();
}

template <typename T>
std::string vec_to_string(const std::vector<T>& v, const std::string_view delim = ",") {
  std::stringstream ss;
  ss << std::setprecision(8) << "[";
  for (const auto& vi : v) {
    ss << vi << delim;
  }
  ss << "]";
  return ss.str();
}

ReferenceLineNLP::ReferenceLineNLP(const Ipopt::SmartPtr<Ipopt::IpoptApplication>& app,
                                   const std::vector<double>& bounds, double w_fem, double w_path,
                                   double w_ref)
    : app_(app), bounds_(bounds), w_fem_(w_fem), w_path_(w_path), w_ref_(w_ref) {
  N_ = bounds.size();  ///< number of points
}

bool ReferenceLineNLP::get_nlp_info(Index& n, Index& m, Index& nnz_jac_g, Index& nnz_h_lag,
                                    IndexStyleEnum& index_style) {
  n = 2 * N_;  // Variables: x0,y0, x1,y1, ..., xN-1,yN-1
  m = 0;       // No nonlinear constraints
  nnz_jac_g = 0;
  nnz_h_lag = 6 * (N_ - 1);  // tri-diagonal in the upper triangular for both x and y

  index_style = TNLP::C_STYLE;
  return true;
}

bool ReferenceLineNLP::get_bounds_info(Index n, Number* x_l, Number* x_u, Index m, Number* g_l,
                                       Number* g_u) {
  T2_PLAN_CHECK(N_ == ref_points.size())
      << "Wrong size: N_=" << N_ << ", ref_points.size()=" << ref_points.size();

  for (size_t i = 0; i < N_; ++i) {
    auto [x_ref, y_ref] = ref_points[i];
    x_l[2 * i] = x_ref - bounds_[i];
    x_u[2 * i] = x_ref + bounds_[i];
    x_l[2 * i + 1] = y_ref - bounds_[i];
    x_u[2 * i + 1] = y_ref + bounds_[i];
  }
  return true;
}

bool ReferenceLineNLP::get_starting_point(Index n, bool init_x, Number* x, bool init_z, Number* z_L,
                                          Number* z_U, Index m, bool init_lambda, Number* lambda) {
  if (init_x) {
    T2_PLAN_CHECK(N_ == ref_points.size())
        << "Wrong size: N_=" << N_ << ", ref_points.size()=" << ref_points.size();

    auto px = x;
    for (size_t i = 0; i < N_; ++i) {
      auto [x_ref, y_ref] = ref_points[i];
      *px++ = x_ref;
      *px++ = y_ref;
    }
  }
  return true;
}

bool ReferenceLineNLP::eval_f(Index n, const Number* x, bool new_x, Number& obj_value) {
  auto [s_fem, s_path, s_ref] = get_obj_value(x);
  // T2_INFO << "s_fem=" << s_fem << ", s_path=" << s_path << ", s_ref=" << s_ref;
  obj_value = w_fem_ * s_fem + w_path_ * s_path + w_ref_ * s_ref;

  return true;
}

bool ReferenceLineNLP::eval_grad_f(Index n, const Number* x, bool new_x, Number* grad_f) {
  std::fill(grad_f, grad_f + n, 0.0);

  for (size_t i = 0; i < N_; ++i) {
    const size_t ix = 2 * i;

    double x_i = x[ix];
    double y_i = x[ix + 1];

    // Reference deviation
    const auto& [x_ref, y_ref] = ref_points[i];
    grad_f[ix] += 2.0 * w_ref_ * (x_i - x_ref);
    grad_f[ix + 1] += 2.0 * w_ref_ * (y_i - y_ref);

    // Path length (i > 0)
    if (i > 0) {
      const size_t iprev = ix - 2;
      double dx = x_i - x[iprev];
      double dy = y_i - x[iprev + 1];

      double delta_x = 2.0 * w_path_ * dx;
      double delta_y = 2.0 * w_path_ * dy;

      grad_f[iprev] -= delta_x;
      grad_f[ix] += delta_x;

      grad_f[iprev + 1] -= delta_y;
      grad_f[ix + 1] += delta_y;
    }

    // FEM (i > 0 and i + 1 < N_)
    if (i > 0 && i + 1 < N_) {
      const size_t iprev = ix - 2;
      const size_t inext = ix + 2;

      double ddx = x[iprev] - 2.0 * x_i + x[inext];
      double ddy = x[iprev + 1] - 2.0 * y_i + x[inext + 1];

      double delta_x = 2.0 * w_fem_ * ddx;
      double delta_y = 2.0 * w_fem_ * ddy;

      grad_f[iprev] += delta_x;
      grad_f[ix] -= 2.0 * delta_x;
      grad_f[inext] += delta_x;

      grad_f[iprev + 1] += delta_y;
      grad_f[ix + 1] -= 2.0 * delta_y;
      grad_f[inext + 1] += delta_y;
    }
  }

  return true;
}

bool ReferenceLineNLP::eval_h(Index n, const Number* x, bool new_x, Number obj_factor, Index m,
                              const Number* lambda, bool new_lambda, Index nele_hess, Index* iRow,
                              Index* jCol, Number* values) {
  if (!values) {
    size_t idx = 0;
    for (size_t i = 0; i < N_; ++i) {
      for (int d = 0; d < 2; ++d) {
        const Index ii = 2 * i + d;  // current index, x_{i}

        /* Diagonal term: ref deviation + path length + FEM */
        iRow[idx] = jCol[idx] = ii;
        ++idx;  // diag

        /* (i-1, i) cross-term: path length + FEM */
        if (i >= 1) {
          iRow[idx] = ii - 2;
          jCol[idx] = ii;
          ++idx;  // path length and FEM
        }

        /* (i-2, i) cross-term: FEM */
        if (i >= 2) {
          iRow[idx] = ii - 4;
          jCol[idx] = ii;
          ++idx;  // FEM
        }
      }  // for d
    }  // for i
    T2_PLAN_CHECK(idx == 6 * (N_ - 1)) << "Wrong size: N_=" << N_ << ", idx=" << idx;

  } else {
    // X for w_fem_, Y for w_path_, Z for w_ref_
    //
    // |X+Y+Z, -2X-Y,   X,       0,       0,       0    |
    // |0,     5X+2Y+Z, -4X-Y,   X,       0,       0    |
    // |0,     0,       6X+2Y+Z, -4X-Y,   X,       0    | * 2
    // |0,     0,       0,       6X+2Y+Z, -4X-Y,   X    |
    // |0,     0,       0,       0,       5X+2Y+Z, -2X-Y|
    // |0,     0,       0,       0,       0,       X+Y+Z|

    std::fill(values, values + nele_hess, 0.0);

    if (hessian_.empty()) {
      size_t idx = 0;
      Number val;  ///< workspace
      hessian_.resize(nele_hess, 0.0);

      for (size_t i = 0; i < N_; ++i) {
        for (int d = 0; d < 2; ++d) {
          /* Diagonal term: ref deviation + path length + FEM */

          // FEM is (x_{i-1} - 2*x_i + x_{i+1})^2
          val = 0.0;      // clear
          val += w_fem_;  // FEM, x_{i-1}
          if (i >= 1 && i + 1 < N_) {
            val += 4 * w_fem_;  // FEM, x_i
          }
          if (i >= 2 && i + 2 < N_) {
            // [1, 5, 6, 6, ..., 6, 5, 1] * X (size: N)
            val += w_fem_;  // FEM, x_{i+1}
          }

          // path length is (x_{i-1} - x_i)^2
          val += w_path_;  // path length, Y
          if (i >= 1 && i + 1 < N_) {
            // [1, 2, 2, ..., 2, 1] * Y (size: N)
            val += w_path_;  // path length, Y
          }

          // reference deviation is (x_i - x_{i,ref})^2
          // [1, 1, 1, ..., 1, 1] * Z (size: N)
          val += w_ref_;  // reference deviation, Z

          hessian_[idx] = val * 2.0;  // assign
          ++idx;                      // increment

          // i is column index, >= row index

          /* (i, i+1) cross-term: FEM */
          val = 0.0;  // clear
          if (i >= 1) {
            val -= w_path_ + 2 * w_fem_;
            if (i + 1 < N_) {
              // [-2, -4, -4, -4, ..., -4, -2] * X (size: N-1)
              // [-1, -1, -1, -1, ..., -1, -1] * Y (size: N-1)
              val -= 2 * w_fem_;
            }

            hessian_[idx] = val * 2.0;  // assign
            ++idx;                      // increment
          }

          /* (i-2, i) cross-term: FEM */
          val = 0.0;  // clear
          if (i >= 2) {
            // [2, 2, ..., 2] * X (size: N-2)
            val = w_fem_;
            hessian_[idx] = val * 2.0;  // assign
            ++idx;                      // increment
          }
        }  // for d
      }  // for i
      T2_PLAN_CHECK(idx == 6 * (N_ - 1)) << "Wrong size: N_=" << N_ << ", idx=" << idx;

    }  // if hessian_.empty()

    auto pval = values;
    auto phessian = hessian_.data();
    for (int i = 0; i < nele_hess; ++i) {
      *pval++ = obj_factor * (*phessian++);
    }
  }  // values == nullptr
  return true;
}

void ReferenceLineNLP::finalize_solution(SolverReturn status, Index n, const Number* x,
                                         const Number* z_L, const Number* z_U, Index m,
                                         const Number* g, const Number* lambda, Number obj_value,
                                         const IpoptData* ip_data,
                                         IpoptCalculatedQuantities* ip_cq) {
  flat_soln_ = std::vector<double>(x, x + N_ * 2);

  soln_.resize(N_);
  auto xp = x;
  for (size_t i = 0; i < N_; ++i) {
    double x = *xp++;
    double y = *xp++;
    soln_[i] = {x, y};
  }
  // T2_INFO << "soln_=" << vec_pair_to_string(soln_);
}

std::tuple<double, double, double> ReferenceLineNLP::get_obj_value(const Number* x) {
  double s_path = 0.0, s_fem = 0.0, s_ref = 0.0;
  for (size_t i = 0; i < N_; ++i) {
    double x_i = x[2 * i];
    double y_i = x[2 * i + 1];

    // Reference deviation
    const auto& [x_ref, y_ref] = ref_points[i];
    double dxr = x_i - x_ref;
    double dyr = y_i - y_ref;
    s_ref += dxr * dxr + dyr * dyr;

    if (i > 0) {
      // Path length
      double dx = x_i - x[2 * (i - 1)];
      double dy = y_i - x[2 * (i - 1) + 1];
      s_path += dx * dx + dy * dy;
    }

    if (i > 0 && i + 1 < N_) {
      // FEM: (x_{i-1} - 2x_i + x_{i+1})^2
      double ddx = x[2 * (i - 1)] - 2 * x_i + x[2 * (i + 1)];
      double ddy = x[2 * (i - 1) + 1] - 2 * y_i + x[2 * (i + 1) + 1];
      s_fem += ddx * ddx + ddy * ddy;
    }
  }

  return {s_fem, s_path, s_ref};
}

std::tuple<double, double, double> ReferenceLineNLP::get_obj_value() {
  if (flat_soln_.size() != N_ * 2) {
    auto nan = std::nan("");
    return {nan, nan, nan};
  }
  return get_obj_value(flat_soln_.data());
}

}  // namespace t2::planning
