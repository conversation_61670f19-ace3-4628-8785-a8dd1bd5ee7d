load("@rules_cc//cc:defs.bzl", "cc_library")

package(default_visibility = ["//visibility:public"])

cc_library(
    name = "fem_pos_deviation_osqp_solver",
    srcs = ["fem_pos_deviation_osqp_solver.cpp"],
    hdrs = ["fem_pos_deviation_osqp_solver.hpp"],
    copts = [
        "-DMODULE_NAME=\\\"planning\\\"",
    ],
    deps = [
        "//src/common/core",  # T2_*
        "//src/planning:planning_module_exception",  # ACHECK
        "//src/planning/config:smoother_config",  # FemPosDeviationSmootherConfig
        "//src/planning/math:optimization_solver_option",
        "//src/planning/math:osqp_postprocess",
        "@com_google_absl//absl/strings",  # StrCat, StrJoin
        "@eigen",
        "@osqp",
    ],
)

cc_library(
    name = "reference_line_nlp",
    srcs = ["reference_line_nlp.cpp"],
    hdrs = ["reference_line_nlp.hpp"],
    copts = [
        "-DMODULE_NAME=\\\"planning\\\"",
    ],
    deps = [
        # "//src/common/core",  # T2_*
        "//src/planning:planning_module_exception",  # PlanningModuleException
        # "//src/planning/config:smoother_config",  # FemPosDeviationSmootherConfig
        # "//src/planning/math:optimization_solver_option",
        # "//src/planning/math:osqp_postprocess",
        # "@com_google_absl//absl/strings",  # StrCat, StrJoin
        # "@eigen",
        # "@osqp",
        "@ipopt",  # IpIpoptApplication
    ],
)
