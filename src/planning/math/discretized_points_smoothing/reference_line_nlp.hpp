#include <cassert>
#include <cmath>
#include <iostream>
#include <vector>

#include <coin/IpIpoptApplication.hpp>  // Ipopt::IpoptApplication
#include <coin/IpSolveStatistics.hpp>
#include <coin/IpTNLP.hpp>
#include <coin/IpTypes.hpp>

namespace t2::planning {

class ReferenceLineNLP : public Ipopt::TNLP {
  using Index = Ipopt::Index;
  using Number = Ipopt::Number;

 public:
  ReferenceLineNLP(const Ipopt::SmartPtr<Ipopt::IpoptApplication>& app,
                   const std::vector<double>& bounds, double w_fem = 1.0, double w_path = 1.0,
                   double w_ref = 1.0);

  virtual bool get_nlp_info(Index& n, Index& m, Index& nnz_jac_g, Index& nnz_h_lag,
                            IndexStyleEnum& index_style) override;

  virtual bool get_bounds_info(Index n, Number* x_l, Number* x_u, Index m, Number* g_l,
                               Number* g_u) override;

  virtual bool get_starting_point(Index n, bool init_x, Number* x, bool init_z, Number* z_L,
                                  Number* z_U, Index m, bool init_lambda, Number* lambda) override;

  virtual bool eval_f(Index n, const Number* x, bool new_x, Number& obj_value) override;

  virtual bool eval_grad_f(Index n, const Number* x, bool new_x, Number* grad_f) override;

  virtual bool eval_g(Index n, const Number* x, bool new_x, Index m, Number* g) override {
    return true;
  }

  virtual bool eval_jac_g(Index n, const Number* x, bool new_x, Index m, Index nele_jac,
                          Index* iRow, Index* jCol, Number* values) override {
    return true;
  }

  virtual bool eval_h(Index n, const Number* x, bool new_x, Number obj_factor, Index m,
                      const Number* lambda, bool new_lambda, Index nele_hess, Index* iRow,
                      Index* jCol, Number* values) override;

  virtual void finalize_solution(Ipopt::SolverReturn status, Index n, const Number* x,
                                 const Number* z_L, const Number* z_U, Index m, const Number* g,
                                 const Number* lambda, Number obj_value,
                                 const Ipopt::IpoptData* ip_data,
                                 Ipopt::IpoptCalculatedQuantities* ip_cq) override;

  std::tuple<double, double, double> get_obj_value(const Number* x);
  std::tuple<double, double, double> get_obj_value();

  const auto& get_flat_solution() const { return flat_soln_; }
  const auto& get_solution() const { return soln_; }

  std::vector<std::pair<double, double>> ref_points;

 private:
  const Ipopt::SmartPtr<Ipopt::IpoptApplication>& app_;

  size_t N_;
  std::vector<double> bounds_;
  double w_fem_ = 1e10;  ///< weight for FEM: (x_{i-1}-2*x_i+x_{i+1})^2
  double w_path_ = 1.0;  ///< weight for path length: (x_{i-1}-x_i)^2
  double w_ref_ = 1.0;   ///< weight for reference deviation: (x_i-x_{i,ref})^2

  std::vector<double> flat_soln_;                ///< flat solution
  std::vector<std::pair<double, double>> soln_;  ///< solution

  // workspace variables
  std::vector<double> dx_;
  std::vector<double> hessian_;
};

}  // namespace t2::planning
