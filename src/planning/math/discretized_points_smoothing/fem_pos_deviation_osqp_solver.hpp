// Copyright (c) 2025 T2 Inc. All rights reserved.

#pragma once

#include <utility>
#include <vector>

#include "osqp/osqp.h"
#include "src/planning/config/smoother_config.hpp"           // FemPosDeviationSmootherConfig
#include "src/planning/math/optimization_solver_option.hpp"  // USE_SHARED_MEMORY_SOLVERS

namespace t2::planning {

class FemPosDeviationOsqpSolver {
 public:
  FemPosDeviationOsqpSolver() = default;
  explicit FemPosDeviationOsqpSolver(const FemPosDeviationSmootherConfig& config);
  void Init(const FemPosDeviationSmootherConfig& config);
  ~FemPosDeviationOsqpSolver();
  std::vector<std::pair<double, double>> Solve(
      const std::vector<std::pair<double, double>>& raw_point2d, const std::vector<double>& bounds);

  std::vector<double> flat_soln;  ///< flat solution

 private:
  void CalculateKernel(std::vector<c_float>& P_data, std::vector<c_int>& P_indices,
                       std::vector<c_int>& P_indptr);

  void CalculateOffset(std::vector<c_float>& q);

  void CalculateAffineConstraint(std::vector<c_float>& A_data, std::vector<c_int>& A_indices,
                                 std::vector<c_int>& A_indptr, std::vector<c_float>& lower_bounds,
                                 std::vector<c_float>& upper_bounds);

  void SetPrimalWarmStart(std::vector<c_float>& primal_warm_start);

  void SetParameters(OSQPSettings& osqp_settings) const;

 private:
  // Reference points and deviation bounds
  std::vector<std::pair<double, double>> ref_points_;
  std::vector<double> bounds_around_refs_;

  // Weights in optimization cost function
  double weight_fem_pos_deviation_ = 1.0e5;
  double weight_path_length_ = 1.0;
  double weight_ref_deviation_ = 1.0;

  OSQPSettings osqp_settings_;  ///< settings of osqp

  // Optimization problem definitions
  int num_of_points_ = 0;
  int num_of_variables_ = 0;
  int num_of_constraints_ = 0;

  struct OSQPProblem {
    // workspace variables that need to persist after FormulateProblem()
    // calculate kernel
    std::vector<c_float> P_data;
    std::vector<c_int> P_indices;
    std::vector<c_int> P_indptr;

    // calculate affine constraints
    std::vector<c_float> A_data;
    std::vector<c_int> A_indices;
    std::vector<c_int> A_indptr;

    // bounds
    std::vector<c_float> lower_bounds;
    std::vector<c_float> upper_bounds;

    // offset
    std::vector<c_float> q;

    // warm start
    std::vector<c_float> primal_warm_start;

    OSQPData data;
    OSQPWorkspace* osqp_work;
  };

  // workspace
  std::map<int, OSQPProblem> map_osqp_problem_;  ///< shared between threads, so need a mutex in
                                                 ///< DiscretePointsReferenceLineSmoother

  std::optional<FemPosDeviationSmootherConfig> opt_config;  ///< assigned after Init
};
}  // namespace t2::planning
