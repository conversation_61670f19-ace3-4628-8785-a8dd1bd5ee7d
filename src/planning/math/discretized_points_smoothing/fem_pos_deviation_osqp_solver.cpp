// Copyright (c) 2025 T2 Inc. All rights reserved.

#include "src/planning/math/discretized_points_smoothing/fem_pos_deviation_osqp_solver.hpp"

#include <limits>
#include <numeric>  // For std::iota

#include "src/planning/math/osqp_postprocess.hpp"
#include "src/planning/planning_module_exception.hpp"  // PlanningModuleException

namespace t2::planning {

FemPosDeviationOsqpSolver::FemPosDeviationOsqpSolver(const FemPosDeviationSmootherConfig& config) {
  Init(config);
}

void FemPosDeviationOsqpSolver::Init(const FemPosDeviationSmootherConfig& config) {
  if (!opt_config) {
    weight_fem_pos_deviation_ = config.weight_fem_pos_deviation;
    weight_path_length_ = config.weight_path_length;
    weight_ref_deviation_ = config.weight_ref_deviation;
    opt_config = config;
  }
}

void FemPosDeviationOsqpSolver::SetParameters(OSQPSettings& osqp_settings) const {
  if (opt_config) {
    osqp_settings.max_iter = opt_config->max_iter;
    osqp_settings.time_limit = opt_config->time_limit;
    osqp_settings.verbose = opt_config->verbose;
    osqp_settings.scaled_termination = opt_config->scaled_termination;
    osqp_settings.warm_start = opt_config->warm_start;
  }
}

std::vector<std::pair<double, double>> FemPosDeviationOsqpSolver::Solve(
    const std::vector<std::pair<double, double>>& raw_point2d, const std::vector<double>& bounds) {
  ref_points_ = raw_point2d;
  bounds_around_refs_ = bounds;

  // Sanity Check
  if (ref_points_.empty()) {
    T2_ERROR << "reference points empty, solver early terminates";
    return {};
  }

  if (ref_points_.size() != bounds_around_refs_.size()) {
    T2_ERROR << "ref_points and bounds size not equal, solver early terminates";
    return {};
  }

  if (ref_points_.size() < 3) {
    T2_ERROR << "ref_points size smaller than 3, solver early terminates";
    return {};
  }

  if (ref_points_.size() > static_cast<size_t>(std::numeric_limits<int>::max())) {
    T2_ERROR << "ref_points size too large, solver early terminates";
    return {};
  }

  // Calculate optimization states definitions
  num_of_points_ = static_cast<int>(ref_points_.size());         // N
  num_of_variables_ = num_of_constraints_ = num_of_points_ * 2;  // 2*N

  const bool has_problem =
      map_osqp_problem_.count(num_of_points_) && map_osqp_problem_.at(num_of_points_).osqp_work;
  if (!has_problem) {
    // Create a OSQPProblem solver for num_of_points_
    map_osqp_problem_.emplace(num_of_points_, OSQPProblem());
  }
  auto& osqp_problem = map_osqp_problem_.at(num_of_points_);

  auto& P_data = osqp_problem.P_data;
  auto& P_indices = osqp_problem.P_indices;
  auto& P_indptr = osqp_problem.P_indptr;

  // calculate affine constraints
  auto& A_data = osqp_problem.A_data;
  auto& A_indices = osqp_problem.A_indices;
  auto& A_indptr = osqp_problem.A_indptr;

  // bounds
  auto& lower_bounds = osqp_problem.lower_bounds;
  auto& upper_bounds = osqp_problem.upper_bounds;

  // offset
  auto& q = osqp_problem.q;

  // warm start
  auto& primal_warm_start = osqp_problem.primal_warm_start;

  auto& data = osqp_problem.data;
  auto& osqp_work = osqp_problem.osqp_work;

  CalculateKernel(P_data, P_indices, P_indptr);
  CalculateAffineConstraint(A_data, A_indices, A_indptr, lower_bounds, upper_bounds);
  CalculateOffset(q);
  SetPrimalWarmStart(primal_warm_start);

  // Define Solver settings
  osqp_set_default_settings(&osqp_settings_);
  SetParameters(osqp_settings_);

  if (!has_problem) {
    data.n = num_of_variables_;    // 2 * N
    data.m = lower_bounds.size();  // 2 * N

    data.P =
        csc_matrix(data.n, data.n, P_data.size(), P_data.data(), P_indices.data(), P_indptr.data());

    data.q = q.data();

    data.A =
        csc_matrix(data.m, data.n, A_data.size(), A_data.data(), A_indices.data(), A_indptr.data());

    data.l = lower_bounds.data();
    data.u = upper_bounds.data();

    osqp_work = osqp_setup(&data, &osqp_settings_);
  } else {
    osqp_update_lin_cost(osqp_work, data.q);
    osqp_update_bounds(osqp_work, data.l, data.u);
  }
  osqp_warm_start_x(osqp_work, primal_warm_start.data());

  // Solve Problem
  osqp_solve(osqp_work);
  const auto& status = osqp_work->info->status_val;
  OSQPPostprocess(status);

  bool res = true;
  if (status < 0) {
    T2_ERROR << "failed optimization status:\t" << osqp_work->info->status;
    res = false;
  }
  if (status != 1 && status != 2) {
    T2_ERROR << "failed optimization status:\t" << osqp_work->info->status;
    res = false;
  }

  if (!res || !osqp_work || !osqp_work->solution) {
    T2_ERROR << "Failed to find solution.";
    return {};
  }

  // Extract primal results
  std::vector<std::pair<double, double>> ret(num_of_points_);
  auto px = osqp_work->solution->x;
  flat_soln = std::vector<double>(px, px + num_of_variables_);
  for (int i = 0; i < num_of_points_; ++i) {
    const double x = *px++;
    const double y = *px++;
    ret[i] = {x, y};
  }

  return ret;
}

void FemPosDeviationOsqpSolver::CalculateKernel(std::vector<c_float>& P_data,
                                                std::vector<c_int>& P_indices,
                                                std::vector<c_int>& P_indptr) {
  T2_PLAN_CHECK(num_of_variables_ >= 4) << "num_of_variables_=" << num_of_variables_;

  // Three quadratic penalties are involved:
  // 1. Penalty x on distance between middle point and point by finite element
  // estimate;
  // 2. Penalty y on path length;
  // 3. Penalty z on difference between points and reference points

  // General formulation of P matrix is as below(with 6 points as an example):
  // I is a two by two identity matrix, X, Y, Z represents x * I, y * I, z * I
  // 0 is a two by two zero matrix
  // |X+Y+Z, -2X-Y,   X,       0,       0,       0    |
  // |0,     5X+2Y+Z, -4X-Y,   X,       0,       0    |
  // |0,     0,       6X+2Y+Z, -4X-Y,   X,       0    |
  // |0,     0,       0,       6X+2Y+Z, -4X-Y,   X    |
  // |0,     0,       0,       0,       5X+2Y+Z, -2X-Y|
  // |0,     0,       0,       0,       0,       X+Y+Z|

  // Only upper triangle needs to be filled
  std::vector<std::vector<std::pair<c_int, c_float>>> columns;
  const int n_cols = num_of_variables_;

  columns.resize(n_cols);

  /* aliases for readability */
  const auto w_f = weight_fem_pos_deviation_;
  const auto w_p = weight_path_length_;
  const auto w_r = weight_ref_deviation_;

  for (int col = 0; col < 2; ++col) {
    columns[col].emplace_back(col, w_f + w_p + w_r);
  }

  for (int col = 2; col < 4; ++col) {
    columns[col].emplace_back(col - 2, -2.0 * w_f - w_p);
    columns[col].emplace_back(col, 5.0 * w_f + 2.0 * w_p + w_r);
  }

  int second_point_from_last_index = num_of_points_ - 2;
  for (int point_index = 2; point_index < second_point_from_last_index; ++point_index) {
    int col_index = point_index * 2;
    for (int col = 0; col < 2; ++col) {
      col_index += col;
      columns[col_index].emplace_back(col_index - 4, w_f);
      columns[col_index].emplace_back(col_index - 2, -4.0 * w_f - w_p);
      columns[col_index].emplace_back(col_index, 6.0 * w_f + 2.0 * w_p + w_r);
    }
  }

  int second_point_col_from_last_col = n_cols - 4;
  int last_point_col_from_last_col = n_cols - 2;
  for (int col = second_point_col_from_last_col; col < last_point_col_from_last_col; ++col) {
    columns[col].emplace_back(col - 4, w_f);
    columns[col].emplace_back(col - 2, -4.0 * w_f - w_p);
    columns[col].emplace_back(col, 5.0 * w_f + 2.0 * w_p + w_r);
  }

  for (int col = last_point_col_from_last_col; col < n_cols; ++col) {
    columns[col].emplace_back(col - 4, w_f);
    columns[col].emplace_back(col - 2, -2.0 * w_f - w_p);
    columns[col].emplace_back(col, w_f + w_p + w_r);
  }

  P_data.clear();
  P_indices.clear();
  P_indptr.resize(n_cols + 1);

  int ind_p = 0;
  for (int i = 0; i < n_cols; ++i) {
    P_indptr[i] = ind_p;
    for (auto [row_ind, coeff] : columns[i]) {
      // Rescale by 2.0 as the quadratic term in osqp default qp problem setup
      // is set as (1/2) * x' * P * x
      P_data.push_back(coeff * 2.0);
      P_indices.push_back(row_ind);
      ++ind_p;
    }
  }
  P_indptr.back() = ind_p;
}

void FemPosDeviationOsqpSolver::CalculateOffset(std::vector<c_float>& q) {
  q.resize(num_of_points_ * 2);
  auto q_ptr = q.data();
  const auto w_r = weight_ref_deviation_;
  for (auto [x_ref, y_ref] : ref_points_) {
    *q_ptr++ = -2.0 * w_r * x_ref;
    *q_ptr++ = -2.0 * w_r * y_ref;
  }
}

void FemPosDeviationOsqpSolver::CalculateAffineConstraint(std::vector<c_float>& A_data,
                                                          std::vector<c_int>& A_indices,
                                                          std::vector<c_int>& A_indptr,
                                                          std::vector<c_float>& lower_bounds,
                                                          std::vector<c_float>& upper_bounds) {
  A_data.resize(num_of_variables_);
  A_indices.resize(num_of_variables_);
  A_indptr.resize(num_of_variables_ + 1);
  std::fill(A_data.begin(), A_data.end(), 1.0);
  std::iota(A_indices.begin(), A_indices.end(), 0);  // 0,1,...,N-1
  std::iota(A_indptr.begin(), A_indptr.end(), 0);    // 0,1,...,N

  lower_bounds.resize(num_of_variables_);
  upper_bounds.resize(num_of_variables_);

  auto l_ptr = lower_bounds.data();
  auto u_ptr = upper_bounds.data();
  auto b_ptr = bounds_around_refs_.data();

  for (auto [x_ref, y_ref] : ref_points_) {
    const double bound_i = *b_ptr++;
    *u_ptr++ = x_ref + bound_i;
    *u_ptr++ = y_ref + bound_i;
    *l_ptr++ = x_ref - bound_i;
    *l_ptr++ = y_ref - bound_i;
  }
}

void FemPosDeviationOsqpSolver::SetPrimalWarmStart(std::vector<c_float>& primal_warm_start) {
  primal_warm_start.resize(num_of_variables_);

  auto p_ptr = primal_warm_start.data();
  for (auto [x_ref, y_ref] : ref_points_) {
    *p_ptr++ = x_ref;
    *p_ptr++ = y_ref;
  }
}

FemPosDeviationOsqpSolver::~FemPosDeviationOsqpSolver() {
  for (auto& [num_of_points, osqp_problem] : map_osqp_problem_) {
    if (osqp_problem.osqp_work) {
      osqp_cleanup(osqp_problem.osqp_work);
    }
    if (osqp_problem.data.P) {
      c_free(osqp_problem.data.P);
    }
    if (osqp_problem.data.A) {
      c_free(osqp_problem.data.A);
    }
  }
}

}  // namespace t2::planning
