"""Terminal UI for run_planning_closed_loop.sh."""

import os
import shutil  # noqa: F401
import signal
import subprocess
import tempfile
from datetime import datetime
from typing import Any, List

import click
from colorama import Fore, init
from pyfzf.pyfzf import FzfPrompt


def kill_processes() -> None:
    commands: List[str] = ["t2_sils_launch", "run_foxglove_bridge"]
    for cmd in commands:
        try:
            output = subprocess.check_output(["pgrep", "-f", cmd]).decode()
            for pid_str in output.strip().split("\n"):
                os.kill(int(pid_str), signal.SIGINT)
                print(f"Killed {cmd} with pid={pid_str}.")
        except subprocess.CalledProcessError:
            print(f"No process {cmd} is running")
            pass


def signal_handler(sig, frame) -> None:
    kill_processes()
    exit(0)


# Register the signal handler
signal.signal(signal.SIGINT, signal_handler)

init(autoreset=True)

# Adjust these paths as appropriate for your environment:
SINGLE_SCENARIO_DIR: str = "closed_loop_scenarios/"  # Where .json files live
"""
SCENARIO_DIRS_BASE: str = (
    "closed_loop_scenarios"  # Where subdirectories containing .json files live
)
SCENARIO_LISTS_DIR: str = "closed_loop_scenarios"  # Where .txt files listing scenarios live
"""

CLOSED_LOOP_FILE_LIST: str = (
    "planning_closed_loop_file_list.txt"  # temporary file list to run_planning_closed_loop.sh
)

up_char = "q"
log_verbose = False
LOG_FILE = "run_planning_closed_loop.log"


def log(message: str):
    """Log to console and to file if log_verbose is enabled, with millisecond timestamps."""
    if log_verbose:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]  # trim to milliseconds
        full_message = f"[{timestamp}] [LOG] {message}"
        print(full_message)
        with open(LOG_FILE, "a") as f:
            f.write(full_message + "\n")


def list_json_scenarios(directory: str) -> List[str]:
    """Return a list of .json files in the given directory and all subdirectories."""
    scenario_files: List[str] = []
    for root, _dirs, files in os.walk(directory):
        for file in files:
            if file.endswith(".json"):
                scenario_files.append(os.path.join(root, file))
    return scenario_files


def list_subdirectories(directory: str) -> List[str]:
    """Return a list of all subdirectories recursively in the given directory."""
    subdirs: List[str] = []
    for root, dirs, _files in os.walk(directory):
        for d in dirs:
            full_path = os.path.join(root, d)
            subdirs.append(os.path.relpath(full_path, directory))
    return sorted(subdirs)


def list_text_files(directory: str) -> List[str]:
    """Return a list of .txt files in the given directory."""
    all_files = os.listdir(directory)
    return [
        f for f in all_files if f.endswith(".txt") and os.path.isfile(os.path.join(directory, f))
    ]


def prompt_user_choice(options: List[str], prompt_message: str = "Select an option:") -> Any:
    """Use pyfzf to let the user fuzzy-search and select an option."""
    if not options:
        print("No options available.")
        return None

    fzf = FzfPrompt()
    print(Fore.YELLOW + prompt_message)
    selected = fzf.prompt(options)
    if selected:
        print(Fore.GREEN + f"You have chosen: {selected[0]}")
        return selected[0]
    else:
        print(Fore.YELLOW + "Selection cancelled.")
        return None


@click.command()
@click.option("--visualize", is_flag=True, help="Enable Foxbox visualization (default: off)")
@click.option("--verbose", is_flag=True, help="Log verbose")
@click.option("--arch", is_flag=True, help="Old or new arch")
def main(visualize: bool, verbose: bool, arch: bool) -> None:
    log_verbose = verbose
    if log_verbose and os.path.exists(LOG_FILE):
        open(LOG_FILE, "w").close()  # Clear previous logs

    print(Fore.MAGENTA + f"arch={arch}")

    SINGLE_SCENARIO_DIR: str = "new_arch_scenarios/" if arch else "closed_loop_scenarios/"

    while True:
        print(Fore.CYAN + "==== Closed Loop Scenario Runner ====")
        print(Fore.YELLOW + "Please select an option:")
        print("1) Run a single scenario")
        """
        print("2) Run all scenarios in a directory")
        print("3) Run from a .txt list of scenarios")
        print("4) Toggle visualization (currently: {})".format("on" if visualize else "off"))
        """
        print(f"{up_char}) Exit")

        choice = "1"

        """
        choice = input(f"Enter your choice 1,2,3,4 or {up_char} to quit: ").strip()

        if choice.lower() == up_char:
            print(Fore.YELLOW + "Exiting.")
            return
        """

        if choice == "1":
            print(Fore.MAGENTA + "You have chosen: Single scenario.")
            scenarios = list_json_scenarios(SINGLE_SCENARIO_DIR)
            if not scenarios:
                print(Fore.RED + f"No .json files found in {SINGLE_SCENARIO_DIR}. Exiting.")
                continue
            selected = prompt_user_choice(scenarios, "Select a scenario by number:")
            if not selected:
                continue
            log(f"Writing single scenario: {selected}")
            with open(CLOSED_LOOP_FILE_LIST, "w") as f:
                f.write(selected + "\n")
        # elif choice == "2":
        #     print(Fore.MAGENTA + "You have chosen: All scenarios in a directory.")
        #     subdirs: List[str] = list_subdirectories(SCENARIO_DIRS_BASE)
        #     subdirs = [SCENARIO_DIRS_BASE] + subdirs
        #     if not subdirs:
        #         print(Fore.RED + f"No subdirectories found in {SCENARIO_DIRS_BASE}. Exiting.")
        #         continue
        #     selected_dir = prompt_user_choice(subdirs, "Select a directory by number:")
        #     if not selected_dir:
        #         continue
        #     full_dir_path = os.path.join(SCENARIO_DIRS_BASE, selected_dir)
        #     scenario_files: List[str] = list_json_scenarios(full_dir_path)
        #     log(f"Writing scenarios from directory: {full_dir_path}")
        #     with open(CLOSED_LOOP_FILE_LIST, "w") as f:
        #         for path in scenario_files:
        #             log(f"Added: {path}")
        #             f.write(path + "\n")

        # elif choice == "3":
        #     print(Fore.MAGENTA + "You have chosen: Run from a .txt list.")
        #     text_files = list_text_files(SCENARIO_LISTS_DIR)
        #     if not text_files:
        #         print(Fore.RED + f"No .txt files found in {SCENARIO_LISTS_DIR}. Exiting.")
        #         continue
        #     selected_txt = prompt_user_choice(
        #         text_files, "Select a scenario list .txt file by number:"
        #     )
        #     if not selected_txt:
        #         continue
        #     full_txt_path = os.path.join(SCENARIO_LISTS_DIR, selected_txt)
        #     log(f"Copying file list from {full_txt_path}")
        #     shutil.copyfile(full_txt_path, CLOSED_LOOP_FILE_LIST)

        # elif choice == "4":
        #     visualize = not visualize
        #     print(Fore.GREEN + f"Visualization {'enabled' if visualize else 'disabled'}.")
        #     continue
        else:
            print(Fore.RED + "Invalid choice. Please try again.")
            continue

        temp_file = tempfile.NamedTemporaryFile(
            "w", delete=False, dir=os.path.dirname(CLOSED_LOOP_FILE_LIST)
        )
        with open(CLOSED_LOOP_FILE_LIST) as input_file:
            for line in input_file:
                file_path = line.strip()
                if os.path.isfile(file_path):
                    log(f"Valid file: {file_path}")
                    temp_file.write(f"{file_path}\n")
                else:
                    print(Fore.RED + f"Warning: File '{file_path}' does not exist.")
        temp_file.close()
        os.replace(temp_file.name, CLOSED_LOOP_FILE_LIST)

        if os.path.getsize(CLOSED_LOOP_FILE_LIST) == 0:
            print(Fore.RED + "No valid scenario files found. Skipping test.")
            continue

        os.environ["PLANNING_CLOSED_LOOP_FILE_LIST"] = "planning_closed_loop_file_list.txt"
        # os.environ["ENABLE_CYBERRT_FORWARDING"] = "1" if visualize else "0"

        try:
            # print(Fore.BLUE + f"Contents of {CLOSED_LOOP_FILE_LIST}:")
            # with open(CLOSED_LOOP_FILE_LIST) as f:
            #     for line in f:
            #         print(Fore.BLUE + line.strip())

            kill_processes()
            cmd = (
                "./run_new_arch_planning_closed_loop.sh"
                if arch
                else "./run_old_arch_planning_closed_loop.sh"
            )

            print(
                Fore.CYAN
                + f"Executing:\ndocker exec -u $USER desktop bash -c cd /home/<USER>/Yatagarasu/src/planning && {cmd}"
            )
            log(
                f"Executing: docker exec -u $USER desktop bash -c cd /home/<USER>/Yatagarasu/src/planning && {cmd}"
            )

            subprocess.run(
                [
                    "docker",
                    "exec",
                    "-u",
                    os.environ["USER"],
                    "desktop",
                    "bash",
                    "-c",
                    f"cd /home/<USER>'USER']}/Yatagarasu/src/planning && {cmd}",
                ],
                check=True,
            )

        except subprocess.CalledProcessError as e:
            print(f"Error running integration_test.sh: {e}")
        except FileNotFoundError:
            print("integration_test.sh not found or not executable.")

        again = input("Would you like to run another test? (y/n): ")
        if again.lower() != "y":
            break


if __name__ == "__main__":
    main()
