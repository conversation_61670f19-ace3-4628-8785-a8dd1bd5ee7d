import re
from pathlib import Path

import click
import matplotlib.pyplot as plt
import numpy as np
from scipy.interpolate import CubicSpline


def parse_vector_pair(s):
    matches = re.findall(r"\((-?[\deE.+-]+),(-?[\deE.+-]+)\)", s)
    return [(float(x), float(y)) for x, y in matches]


def parse_vector(s):
    matches = re.findall(r"-?[\deE.+-]+", s)
    return [float(v) for v in matches]


def extract_log_components(line):
    input_match = re.search(r"input=(\[.*?\])", line)
    bounds_match = re.search(r"bounds=(\[.*?\])", line)
    output_match = re.search(r"output=(\[.*?\])", line)
    return (
        input_match.group(1) if input_match else None,
        bounds_match.group(1) if bounds_match else None,
        output_match.group(1) if output_match else None,
    )


def visualize_log_line(input_str, bounds_str, output_str, title="Curve Smoother Output"):
    if not input_str or not output_str:
        print("Missing input or output data.")
        return

    input_pts = parse_vector_pair(input_str)
    output_pts = parse_vector_pair(output_str)

    input_x, input_y = zip(*input_pts)
    output_x, output_y = zip(*output_pts)

    # Compute arc length s for output points
    output_pts = list(zip(output_x, output_y))
    s = [0.0]
    for i in range(1, len(output_pts)):
        dx = output_pts[i][0] - output_pts[i - 1][0]
        dy = output_pts[i][1] - output_pts[i - 1][1]
        s.append(s[-1] + np.hypot(dx, dy))
    s = np.array(s)

    # Build cubic splines x(s) and y(s)
    cs_x = CubicSpline(s, output_x, bc_type="natural")
    cs_y = CubicSpline(s, output_y, bc_type="natural")

    # Evaluate splines
    s_dense = np.linspace(s[0], s[-1], 300)
    x_smooth = cs_x(s_dense)
    y_smooth = cs_y(s_dense)

    # Plot
    plt.figure(figsize=(9, 5))
    plt.plot(input_x, input_y, "o-", label="Input Points", alpha=0.6)
    plt.plot(output_x, output_y, "s-", label="Output Points", alpha=0.6)
    plt.plot(x_smooth, y_smooth, "-", label="Cubic Spline (Arc Length Param)", linewidth=2)

    plt.title(title)
    plt.xlabel("x")
    plt.ylabel("y")
    plt.legend()
    plt.grid(True)
    plt.tight_layout()
    plt.show()


@click.command()
@click.argument("file_path", type=click.Path(exists=True, path_type=Path))
@click.option("--all", is_flag=True, help="Plot all matching entries")
@click.option("--save", is_flag=True, help="Save plots as PNG instead of displaying them")
def main(file_path, all, save):

    with open(file_path) as f:
        lines = [line.strip() for line in f if line.strip()]

    matched = []
    buffer = {}
    for line in lines:
        if line.startswith("input="):
            buffer["input"] = line
        elif line.startswith("bounds="):
            buffer["bounds"] = line
        elif line.startswith("output="):
            buffer["output"] = line
            if "input" in buffer and "bounds" in buffer:
                matched.append((buffer["input"], buffer["bounds"], buffer["output"]))
            buffer = {}

    if not matched:
        click.echo("No valid log entries found.")
        return

    if all:
        for i, (input_line, bounds_line, output_line) in enumerate(matched):
            input_str, _, _ = extract_log_components(input_line)
            _, bounds_str, _ = extract_log_components(bounds_line)
            _, _, output_str = extract_log_components(output_line)
            title = f"Curve Smoother Output #{i + 1}"
            visualize_log_line(input_str, bounds_str, output_str, title)
    else:
        input_line, bounds_line, output_line = matched[-1]
        input_str, _, _ = extract_log_components(input_line)
        _, bounds_str, _ = extract_log_components(bounds_line)
        _, _, output_str = extract_log_components(output_line)
        title = "Latest Curve Smoother Output"
        visualize_log_line(input_str, bounds_str, output_str, title)


if __name__ == "__main__":
    main()
