#pragma once

// Copyright (c) 2025 T2 Inc. All rights reserved.
#include <sstream>
#include <string_view>
#include <vector>

namespace t2::planning::test {

template <typename T>
inline std::string vec_to_string(const std::vector<std::pair<T, T>>& vp,
                                 const std::string_view delim = ",") {
  std::stringstream ss;
  ss << std::setprecision(8) << "[";
  for (const auto& [first, second] : vp) {
    ss << "(" << first << "," << second << ")" << delim;
  }
  ss << "]";
  return ss.str();
}

template <typename T>
inline std::string vec_to_string(const std::vector<T>& v, const std::string_view delim = ",") {
  std::stringstream ss;
  ss << std::setprecision(8) << "[";
  for (const auto& vi : v) {
    ss << vi << delim;
  }
  ss << "]";
  return ss.str();
}

}  // namespace t2::planning::test
