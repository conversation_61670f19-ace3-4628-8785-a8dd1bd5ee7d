// Copyright (c) 2025 T2 Inc. All rights reserved.

#include <iostream>
#include <sstream>

#include <casadi/casadi.hpp>

#include "absl/debugging/failure_signal_handler.h"  // for absl::FailureSignalHandler
#include "apex_init/apex_init.hpp"                  // for apex::pre_init and apex::post_init
#include "common_msgs/msg/health.hpp"
#include "cpputils/common_exceptions.hpp"   // for apex::runtime_error
#include "executor2/executor_factory.hpp"   // for apex::executor::executor_factory
#include "executor2/executor_runner.hpp"    // for apex::executor::executor_runner
#include "interrupt/interrupt_handler.hpp"  // for apex::interrupt_handler
#include "planning_closed_loop.hpp"         // FromFile
#include "planning_test_input.hpp"          // PlanningTestInput
#include "scenario_file.hpp"                // ScenarioFile
#include "src/planning/math/expression_parser/expression_parser.hpp"  // ExpressionParser
#include "src/planning/planning_on_local_map_component/internal_data_definition.hpp"
#include "src/planning/planning_on_local_map_component/planning_on_local_map_component.hpp"  // PlanningOnLocalMapComponent
#include "src/planning/planning_on_local_map_component/prototype_planner/prototype_planner.hpp"  // CartesianTrajectory
#include "timer.hpp"  // Timer

/*
Usage:

rm -rf /home/<USER>/data && mkdir /home/<USER>/data && bazel run
//src/planning/integration_tests:test_planning_online_mapping

*/

#include "src/common/runfiles/runtime_dependency_manager.hpp"  // RuntimeDependencyManager
#include "src/planning/planning_on_local_map_component/simulation.hpp"

namespace t2_planning = ::t2::planning;
namespace test = t2_planning::test;
namespace common = ::t2::common;

using common::runfiles::RuntimeDependencyManager;

std::vector<std::pair<double, double>> ConstructCentralCurvePoints(
    const size_t n_points, const double dx, std::function<double(double)> func, double x = 0.0) {
  std::vector<std::pair<double, double>> global_center_lane_curve;
  global_center_lane_curve.reserve(n_points);

  for (size_t i = 0; i < n_points; ++i) {
    global_center_lane_curve.emplace_back(x, func(x));  ///< (x, f(x))
    x += dx;
  }

  return global_center_lane_curve;
}

int main(int argc, char** const argv) {
  T2_INFO << "argc=" << argc;
  for (int i = 0; i < argc; ++i) {
    T2_INFO << "argv[" << i << "] = " << argv[i];
  }

  ScenarioFile scenario_file;
  test::FromFile(scenario_file,
                 RuntimeDependencyManager::ResolvePath(
                     "src/planning/integration_tests/planning_test_input_filename.json"));
  T2_INFO << t2_planning::ToJsonString(scenario_file);

  auto planning_test_input_filename =
      RuntimeDependencyManager::ResolvePath(scenario_file.planning_test_input_filename);

  /* ===== Scenario =====*/
  test::PlanningTestInput planning_test_input;
  if (!test::FromFile(planning_test_input, planning_test_input_filename)) {
    throw t2_planning::PlanningModuleException("Invalid planning test input",
                                               t2_planning::PlanningModuleErrorCode::INVALID_INPUT);
  }

  const std::string lane_center_curve_expression = planning_test_input.lane_center_curve_expression;

  auto expression_func =
      ExpressionParser::parse_expression(lane_center_curve_expression);  // TODO: Use this function
  T2_INFO << "Parsed expression function evaluation at [0, 1] = [" << expression_func(0) << ", "
          << expression_func(1) << "]";

  const size_t num_steps = planning_test_input.num_runs;
  const double dx = 0.1;            ///< resolution in distance [m], same as center_line_sampling
  const double dt = 0.1;            ///< resolution in time [s]
  const double max_v = 80.0 / 3.6;  ///< 80 kph
  const double x_look_backward_distance = 1.0;
  const double x_look_forward_distance = 300.0;  ///< center_line_length
  const int num_points = 60;                     ///< num_points*dt = 6 sec is the planning horizon
  const size_t n_points = static_cast<int>(
      std::ceil(((num_steps + num_points) * dt * max_v + x_look_forward_distance) / dx));
  T2_INFO << "num_steps=" << num_steps << ", max_v=" << max_v << ", n_points=" << n_points;
  const auto global_center_lane_curve = ConstructCentralCurvePoints(n_points, dx, expression_func);

  const auto& localization_info = planning_test_input.localization_info;
  auto ego_state_global = t2_planning::ConstructVehicleState(
      localization_info.x, localization_info.y, localization_info.heading, localization_info.vy,
      localization_info.ay);

  t2_planning::PlanningVehicleState ego_state_local = ego_state_global;
  ego_state_local.heading = 0.0;

  std::map<int, t2_planning::Obstacle> obstacles;  // TODO set data

  absl::InstallFailureSignalHandler(absl::FailureSignalHandlerOptions());
  int ret = 0;

  try {
    auto scoped_init = apex::scoped_init(argc, argv, false);
    const apex::interrupt_handler::installer interrupt_handler_installer{};
    const auto executor = ::apex::executor::executor_factory::create();
    scoped_init.post_init();

    t2_planning::PlanningOnLocalMapComponent planning_component;

    static constexpr char const* kTrajectoryTopic = "/t2/planning/global";
    auto trajectory_writer = planning_component.CreateTrajectoryWriter(kTrajectoryTopic);

    using namespace std::chrono_literals;
    const apex::executor::executor_runner runner{apex::executor::executor_runner::deferred,
                                                 *executor};
    trajectory_writer->wait_for_matched(1, 5s);

    t2_planning::RoadInformation local_map_information;

    /* ===== Théo's code ===== */

    // Initialize CasADi that is required for bazel build system
    casadi::GlobalOptions::casadipath = "external/casadi/libcasadi/lib";
    /* ===== (end of) Théo's code ===== */

    t2_planning::PlanningTestMessage planning_test_message;
    planning_test_message.filename = planning_test_input_filename;
    planning_test_message.scenario = t2_planning::ToJsonString(planning_test_input);
    planning_test_message.command = "scenario";

    for (size_t iter = 0; iter < num_steps; ++iter) {
      T2_INFO << "iter=" << iter << ", ego_state_global.x=" << ego_state_global.x;

      auto it_begin = t2_planning::find_begin(global_center_lane_curve, ego_state_global.x,
                                              x_look_backward_distance);

      auto it_end = t2_planning::find_end(global_center_lane_curve, ego_state_global.x,
                                          x_look_forward_distance);

      T2_INFO << "x_begin=" << it_begin->first << ", x_end=" << it_end->first;

      t2_planning::ADCTrajectory adc_trajectory;
      double elapsed_time;

      // plan in local frame
      t2_planning::GetCenterLaneCurve(local_map_information.lane_center_curves,
                                      /*input*/ ego_state_global, it_begin, it_end);
      test::Timer timer;
      adc_trajectory = planning_component.Plan(ego_state_local, obstacles, local_map_information);
      elapsed_time = timer.elapsed();

      // record [0]
      auto& only_trajectory = adc_trajectory.only_trajectory;
      auto& traj_path_point = only_trajectory[0].path_point;
      traj_path_point.x = ego_state_global.x;
      traj_path_point.y = ego_state_global.y;
      only_trajectory[0].v = ego_state_global.linear_velocity;
      only_trajectory[0].a = ego_state_global.linear_acceleration;
      trajectory_writer->publish(adc_trajectory);  ///< publish global (x,y) for [0]

      // update global localization by [1]
      // plan in local frame
      if (only_trajectory.size() > 1) {
        ego_state_global = t2_planning::UpdateGlobalPose(only_trajectory[1], ego_state_global);
      }
      // update v and a
      ego_state_local.linear_velocity = ego_state_global.linear_velocity;
      ego_state_local.linear_acceleration = ego_state_global.linear_acceleration;

      planning_test_message.elapsed_time = elapsed_time;
      planning_component.planning_test_writer()->publish(planning_test_message);
      T2_INFO << "Finished iter=" << iter;
    }

  } catch (const std::exception& e) {
    if (rclcpp::ok()) {
      APEX_FATAL_R(e.what());
    } else {
      std::cerr << e.what() << "\n";
    }
    ret = 2;
  } catch (...) {
    if (rclcpp::ok()) {
      APEX_FATAL_R("Unknown error occurred");
    } else {
      std::cerr << "Unknown error occurred" << "\n";
    }
    ret = -1;
  }

  T2_INFO << "Before ret=" << ret;

  return ret;
}
