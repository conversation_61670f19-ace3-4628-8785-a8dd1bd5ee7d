// Copyright (c) 2025 T2 Inc. All rights reserved.
#include <chrono>  // for std::chrono::milliseconds
#include <cmath>   //sincos
#include <iostream>
#include <memory>  // for std::make_shared<>
#include <sstream>
#include <thread>
#include <utility>  // for std::move

#include "src/common/runfiles/runtime_dependency_manager.hpp"  // RuntimeDependencyManager
#include "src/planning/math/discretized_points_smoothing/fem_pos_deviation_osqp_solver.hpp"  // FemPosDeviationOsqpSolver
#include "src/planning/planning_module_exception.hpp"  // PlanningModuleException
#include "timer.hpp"                                   // Timer
#include "vec_to_string.hpp"                           // vec_to_string

namespace t2_planning = ::t2::planning;
namespace common = ::t2::common;
namespace test = t2_planning::test;

using common::runfiles::RuntimeDependencyManager;

/*
Usage:

  ~/Yatagarasu/bazel-bin/src/planning/integration_tests/curve_smoother

  bazel run //src/planning/integration_tests:curve_smoother
*/

int main(const int argc, char** const argv) {
  t2_planning::FemPosDeviationOsqpSolver solver{t2_planning::FemPosDeviationSmootherConfig{}};

  const double radius = 50.0;
  const size_t n_points = ceil(radius * 2.0 * M_PI);
  const double resolution = 2.0 * M_PI / n_points;
  std::vector<std::pair<double, double>> input(n_points);

  double theta = 0.0;
  double cth = 1.0, sth = 0.0;
  for (size_t i = 0; i < n_points; ++i) {
    sincos(theta, &sth, &cth);
    const double error = (i % 2) ? 0.25 : -0.25;
    input[i] = {radius * cth + error, radius * sth - error};
    theta += resolution;
  }

  const double bound = 0.5;
  std::vector<double> bounds(n_points, bound);

  auto output = solver.Solve(input, bounds);

  T2_INFO << "\n"
          << "input=" << test::vec_to_string(input)
          << "\n"
             "bounds="
          << test::vec_to_string(bounds)
          << "\n"
             "output="
          << test::vec_to_string(output);
  return 0;
}
